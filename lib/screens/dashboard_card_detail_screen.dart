import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../constants/app_colors.dart';
import '../constants/size.dart';
import '../widgets/smart_svg_icon.dart';
import '../widgets/platform_icon.dart';
import '../widgets/custom_text_field.dart';
import '../controllers/dashboard_card_detail_controller.dart';
import '../models/order_model.dart';

class DashboardCardDetailScreen extends GetView<DashboardCardDetailController> {
  const DashboardCardDetailScreen({super.key});

  @override
  Widget build(BuildContext context) {
    MySize().init(context);

    return Scaffold(
      backgroundColor: AppColors.backgroundColor,
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return Scaffold(
              backgroundColor: AppColors.backgroundColor,
              appBar: _buildMobileAppBar(),
              drawer: _buildMobileDrawer(),
              body: _buildMainContent(),
            );
          }

          return Row(
            children: [
              // Left Sidebar
              _buildSidebar(),

              // Main Content
              Expanded(child: _buildMainContent()),
            ],
          );
        },
      ),
    );
  }

 PreferredSizeWidget _buildMobileAppBar() {
    return AppBar(
      backgroundColor: AppColors.primaryColor,
      elevation: 0,
      leading: Builder(
        builder: (context) => IconButton(
          icon: PlatformIcon(
            iconName: 'menu',
            size: MySize.size24,
            color: AppColors.blackColor,
          ),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          SmartIcon(
            assetPath: 'assets/icons/logo_icon.svg',
            height: MySize.size80,
            width: MySize.size84,
            color: AppColors.blackColor,
          ),
        ],
      ),
      centerTitle: false,
    );
  }

  Widget _buildSidebar() {
    return Container(
      width: MySize.size200,
      color: AppColors.primaryColor,
      child: Column(
        children: [
          // Logo Section
          Container(
            padding: EdgeInsets.all(MySize.size20),
            child: Column(
              children: [
                SmartIcon(
                  assetPath: 'assets/icons/logo_icon.svg',
                  height: MySize.size70,
                  width: MySize.size129,
                  color: AppColors.blackColor,
                ),
              ],
            ),
          ),

          // Navigation Items
          Expanded(
            child: Obx(
              () => Column(
                children: [
                  _buildNavItem(
                    icon: 'home',
                    label: 'Dashboard',
                    isSelected: controller.selectedNavIndex.value == 0,
                    onTap: () => controller.selectNavItem(0),
                  ),
                  _buildNavItem(
                    icon: 'person',
                    label: 'Customers List',
                    isSelected: controller.selectedNavIndex.value == 1,
                    onTap: () => controller.selectNavItem(1),
                  ),
                  _buildNavItem(
                    icon: 'shopping_cart',
                    label: 'Orders List',
                    isSelected: controller.selectedNavIndex.value == 2,
                    onTap: () => controller.selectNavItem(2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: MySize.size16,
        vertical: MySize.size4,
      ),
      decoration: BoxDecoration(
        color: isSelected ? Colors.white : Colors.transparent,
        borderRadius: BorderRadius.circular(MySize.size8),
      ),
      child: ListTile(
        leading: PlatformIcon(
          iconName: icon,
          size: MySize.size20,
          color: isSelected ? AppColors.blackColor : Colors.white,
        ),
        title: Text(
          label,
          style: TextStyle(
            fontSize: MySize.size14,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
            color: isSelected ? AppColors.blackColor : Colors.white,
          ),
        ),
        onTap: onTap,
      ),
    );
  }

  Widget _buildMobileDrawer() {
    return Drawer(
      backgroundColor: AppColors.primaryColor,
      child: SafeArea(
        child: Column(
          children: [
            // Logo Section
            Container(
              padding: EdgeInsets.all(MySize.size20),
              child: Column(
                children: [
                  SmartIcon(
                    assetPath: 'assets/icons/logo_icon.svg',
                    height: MySize.size100,
                    width: MySize.size100,
                    color: AppColors.blackColor,
                  ),
                ],
              ),
            ),

            // Divider
            Divider(
              color: AppColors.blackColor.withValues(alpha: 0.2),
              thickness: 1,
              indent: MySize.size16,
              endIndent: MySize.size16,
            ),

            // Navigation Items
            Expanded(
              child: Obx(
                () => Column(
                  children: [
                    _buildMobileNavItem(
                      icon: 'home',
                      label: 'Dashboard',
                      isSelected: controller.selectedNavIndex.value == 0,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(0);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'person',
                      label: 'Customers List',
                      isSelected: controller.selectedNavIndex.value == 1,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(1);
                      },
                    ),
                    _buildMobileNavItem(
                      icon: 'shopping_cart',
                      label: 'Orders List',
                      isSelected: controller.selectedNavIndex.value == 2,
                      onTap: () {
                        Navigator.of(Get.context!).pop(); // Close drawer
                        controller.selectNavItem(2);
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileNavItem({
    required String icon,
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: MySize.size12,
          vertical: MySize.size4,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: MySize.size16,
          vertical: MySize.size12,
        ),
        decoration: BoxDecoration(
          color: isSelected ? Colors.white : Colors.transparent,
          borderRadius: BorderRadius.circular(MySize.size8),
        ),
        child: Row(
          children: [
            PlatformIcon(
              iconName: icon,
              size: MySize.size20,
              color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
            ),
            SizedBox(width: MySize.size12),
            Expanded(
              child: Text(
                label,
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected ? AppColors.primaryColor : AppColors.blackColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return Padding(
      padding: EdgeInsets.all(MySize.size24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with back button
          _buildHeader(),

          SizedBox(height: MySize.size24),

          // Search and Action Bar
          _buildSearchAndActionBar(),

          SizedBox(height: MySize.size24),

          // Orders List
          Expanded(child: _buildOrdersList()),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Back Button
        GestureDetector(
          onTap: controller.onBackToDashboard,
          child: Container(
            padding: EdgeInsets.all(MySize.size8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(MySize.size8),
              border: Border.all(color: AppColors.borderColor),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PlatformIcon(
                  iconName: 'back',
                  size: MySize.size16,
                  color: AppColors.textPrimary,
                ),
                SizedBox(width: MySize.size8),
                Text(
                  'Back to Dashboard',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
          ),
        ),

        SizedBox(width: MySize.size16),

        // Title
        Obx(() => Text(
          controller.cardTitle.value,
          style: TextStyle(
            fontSize: MySize.size24,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        )),
      ],
    );
  }

  Widget _buildSearchAndActionBar() {
    return Container(
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Search Bar
          Expanded(
            child: CustomTextField(
              controller: controller.searchController,
              hintText: 'Search Orders..',
              prefixIcon: Padding(
                padding: EdgeInsets.all(MySize.size12),
                child: PlatformIcon(
                  iconName: 'search',
                  size: MySize.size20,
                  color: AppColors.primaryColor,
                ),
              ),
              fillColor: AppColors.backgroundColor,
              borderColor: AppColors.borderColor,
              borderRadius: MySize.size8,
              contentPadding: EdgeInsets.symmetric(
                horizontal: MySize.size16,
                vertical: MySize.size12,
              ),
            ),
          ),

          SizedBox(width: MySize.size16),

          // Add Order Button
          ElevatedButton(
            onPressed: controller.onAddOrderTap,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(
                horizontal: MySize.size20,
                vertical: MySize.size12,
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(MySize.size8),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                PlatformIcon(
                  iconName: 'add',
                  size: MySize.size18,
                  color: Colors.white,
                ),
                SizedBox(width: MySize.size8),
                Text(
                  'Add Order',
                  style: TextStyle(
                    fontSize: MySize.size14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrdersList() {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      final orders = controller.filteredOrders;
      if (orders.isEmpty) {
        return const Center(
          child: Text('No orders found'),
        );
      }

      return LayoutBuilder(
        builder: (context, constraints) {
          final isMobile = constraints.maxWidth <= 768;

          if (isMobile) {
            return ListView.builder(
              padding: EdgeInsets.all(MySize.size8),
              itemCount: orders.length,
              itemBuilder: (context, index) {
                return _buildMobileOrderCard(orders[index]);
              },
            );
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: MySize.size16,
              mainAxisSpacing: MySize.size16,
              childAspectRatio: 2.8,
            ),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              return _buildOrderCard(orders[index]);
            },
          );
        },
      );
    });
  }

  Widget _buildOrderCard(OrderModel order) {
    final priority = controller.getPriority(order);
    final priorityColor = controller.getPriorityColor(priority);
    final notes = controller.getNotes(order);

    return Container(
      padding: EdgeInsets.all(MySize.size12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order ID and Priority Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order ID: ${order.orderId}',
                style: TextStyle(
                  fontSize: MySize.size18,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size8,
                  vertical: MySize.size4,
                ),
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Text(
                  priority,
                  style: TextStyle(
                    fontSize: MySize.size12,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Customer Info
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Customer Name',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      order.customerName,
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: MySize.size2),
                    Text(
                      order.customerPhone,
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Product Type',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      'Art Paper',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Items',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '${order.itemCount} Items',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Price',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '₹ ${order.amount.toInt()}',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Date',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      _formatDate(order.orderDate),
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Items, Price, and Date Row
          // Row(
          //   children: [
          //     Expanded(
          //       child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Text(
          //             'Items',
          //             style: TextStyle(
          //               fontSize: MySize.size12,
          //               color: AppColors.textSecondary,
          //             ),
          //           ),
          //           Text(
          //             '${order.itemCount} Items',
          //             style: TextStyle(
          //               fontSize: MySize.size14,
          //               fontWeight: FontWeight.w500,
          //               color: AppColors.textPrimary,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //     Expanded(
          //       child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Text(
          //             'Price',
          //             style: TextStyle(
          //               fontSize: MySize.size12,
          //               color: AppColors.textSecondary,
          //             ),
          //           ),
          //           Text(
          //             '₹ ${order.amount.toInt()}',
          //             style: TextStyle(
          //               fontSize: MySize.size14,
          //               fontWeight: FontWeight.w600,
          //               color: AppColors.textPrimary,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //     Expanded(
          //       child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Text(
          //             'Order Date',
          //             style: TextStyle(
          //               fontSize: MySize.size12,
          //               color: AppColors.textSecondary,
          //             ),
          //           ),
          //           Text(
          //             _formatDate(order.orderDate),
          //             style: TextStyle(
          //               fontSize: MySize.size14,
          //               fontWeight: FontWeight.w500,
          //               color: AppColors.textPrimary,
          //             ),
          //           ),
          //         ],
          //       ),
          //     ),
          //   ],
          // ),

          SizedBox(height: MySize.size8),

          // Notes
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notes',
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                notes,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size12),

          // Action Buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => controller.onViewDetails(order),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppColors.borderColor),
                    padding: EdgeInsets.symmetric(vertical: MySize.size8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size6),
                    ),
                  ),
                  child: Text(
                    'View Details',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ),
              SizedBox(width: MySize.size8),
              Expanded(
                child: OutlinedButton(
                  onPressed: () => controller.onEditOrder(order),
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: AppColors.borderColor),
                    padding: EdgeInsets.symmetric(vertical: MySize.size8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size6),
                    ),
                  ),
                  child: Text(
                    'Edit Order',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      color: AppColors.textPrimary,
                    ),
                  ),
                ),
              ),
              SizedBox(width: MySize.size8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => controller.onUpdateStatus(order),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: MySize.size8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size6),
                    ),
                  ),
                  child: Text(
                    'Update Status',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
              SizedBox(width: MySize.size8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {},
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: MySize.size8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size6),
                    ),
                  ),
                  child: Text(
                    'Update Priority',
                    style: TextStyle(
                      fontSize: MySize.size12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMobileOrderCard(OrderModel order) {
    final priority = controller.getPriority(order);
    final priorityColor = controller.getPriorityColor(priority);
    final notes = controller.getNotes(order);

    return Container(
      margin: EdgeInsets.only(bottom: MySize.size12),
      padding: EdgeInsets.all(MySize.size16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(MySize.size12),
        border: Border.all(color: AppColors.borderColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order ID and Priority Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Order ID: ${order.orderId}',
                style: TextStyle(
                  fontSize: MySize.size14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: MySize.size8,
                  vertical: MySize.size4,
                ),
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(MySize.size12),
                ),
                child: Text(
                  priority,
                  style: TextStyle(
                    fontSize: MySize.size10,
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size12),

          // Customer Info
          Text(
            'Customer Name',
            style: TextStyle(
              fontSize: MySize.size12,
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            order.customerName,
            style: TextStyle(
              fontSize: MySize.size14,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
            ),
          ),
          Text(
            order.customerPhone,
            style: TextStyle(
              fontSize: MySize.size12,
              color: AppColors.textSecondary,
            ),
          ),

          SizedBox(height: MySize.size12),

          // Product Type and Items
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Product Type',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      'Art Paper',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Items',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '${order.itemCount} Items',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size12),

          // Price and Date
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Price',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      '₹ ${order.amount.toInt()}',
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Order Date',
                      style: TextStyle(
                        fontSize: MySize.size12,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Text(
                      _formatDate(order.orderDate),
                      style: TextStyle(
                        fontSize: MySize.size14,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size12),

          // Notes
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notes',
                style: TextStyle(
                  fontSize: MySize.size12,
                  color: AppColors.textSecondary,
                ),
              ),
              Text(
                notes,
                style: TextStyle(
                  fontSize: MySize.size14,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),

          SizedBox(height: MySize.size16),

          // Action Buttons
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => controller.onViewDetails(order),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.borderColor),
                        padding: EdgeInsets.symmetric(vertical: MySize.size8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(MySize.size6),
                        ),
                      ),
                      child: Text(
                        'View Details',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: MySize.size8),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => controller.onEditOrder(order),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: AppColors.borderColor),
                        padding: EdgeInsets.symmetric(vertical: MySize.size8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(MySize.size6),
                        ),
                      ),
                      child: Text(
                        'Edit Order',
                        style: TextStyle(
                          fontSize: MySize.size12,
                          color: AppColors.textPrimary,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: MySize.size8),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => controller.onUpdateStatus(order),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: MySize.size12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(MySize.size6),
                    ),
                  ),
                  child: Text(
                    'Update Status',
                    style: TextStyle(
                      fontSize: MySize.size14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}
